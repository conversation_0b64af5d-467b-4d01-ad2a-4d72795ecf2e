using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.Linq;

public enum AreaSize
{
    Area_20x20,
    Area_50x50,
    Area_100x100,
    Custom
}

public enum PaintMode
{
    Traditional,
    SplineBased,
    FieldPattern
}

[System.Serializable]
public class SplinePoint
{
    public Vector3 position;
    public Vector3 tangent;
    public float width = 2f;
    public float intensity = 1f;

    public SplinePoint(Vector3 pos)
    {
        position = pos;
        tangent = Vector3.forward;
    }
}

public class TerrainPainter : MonoBehaviour
{
    [Header("Paint Mode Settings")]
    public PaintMode paintMode = PaintMode.Traditional;

    [Header("Traditional Paint Settings")]
    public float paintRadius = 2f;
    public int targetLayerIndex = 0;
    public int applyLayerIndex = 1;
    public float minSpeed = 0.1f;
    public float maxSpeed = 10f;
    public float paintIntensity = 1f;
    public bool useSquareBrush = false;
    public Vector3 paintOffset = Vector3.zero;
    public bool showDebugGizmos = false;
    public float paintFrequency = 0.1f;

    [Header("Spline-Based Paint Settings")]
    public List<SplinePoint> splinePoints = new List<SplinePoint>();
    public float splineResolution = 0.5f;
    public bool autoCreateSpline = true;
    public float splineWidth = 3f;
    public AnimationCurve splineIntensityCurve = AnimationCurve.Linear(0, 1, 1, 1);
    public bool smoothSplinePainting = true;
    public float splineSmoothing = 2f;

    [Header("Field Pattern Settings")]
    public bool useFieldPattern = false;
    public float fieldRowSpacing = 3f;
    public float fieldTurnRadius = 5f;
    public bool alternateDirection = true;

    [Header("Area Completion Settings")]
    public AreaSize areaSize = AreaSize.Area_50x50;
    public Vector2 customAreaSize = new Vector2(50, 50);
    public GameObject completionPanel;
    public Text percentageText;
    public Slider progressSlider;
    public float targetCompletionPercentage = 100f;

    [Header("Area Progress")]
    public float currentCompletionPercentage = 0f;
    public bool areaCompleted = false;

    private Terrain terrain;
    private TerrainData terrainData;
    private float[,,] originalAlphamaps;
    private Vector3 lastPosition;
    private Vector3 velocity;
    private Renderer objectRenderer;
    private Vector3 lastPaintPosition;
    private Quaternion currentBrushRotation = Quaternion.identity;

    private Vector2 currentAreaSize;
    private int totalAreaPixels;
    private int paintedPixels;

    // Spline-based painting variables
    private List<Vector3> splinePath = new List<Vector3>();
    private int currentSplineIndex = 0;
    private float splineProgress = 0f;
    private bool isRecordingSpline = false;
    private float lastSplineRecordTime = 0f;
    private Vector3 splineStartPosition;
    private List<Vector3> recordedPath = new List<Vector3>();

    void Start()
    {
        terrain = Terrain.activeTerrain;
        objectRenderer = GetComponent<Renderer>();

        if (terrain != null)
        {
            terrainData = terrain.terrainData;
            originalAlphamaps = terrainData.GetAlphamaps(0, 0, terrainData.alphamapWidth, terrainData.alphamapHeight);
        }

        lastPosition = GetObjectCenter();
        lastPaintPosition = lastPosition + paintOffset;
        InitializeAreaTracking();
    }

    Vector3 GetObjectCenter()
    {
        return objectRenderer != null ? objectRenderer.bounds.center : transform.position;
    }

    void Update()
    {
        Vector3 currentCenter = GetObjectCenter();
        velocity = (currentCenter - lastPosition) / Time.deltaTime;

        if (velocity.sqrMagnitude > 0.001f)
        {
            float angle = Mathf.Atan2(velocity.x, velocity.z) * Mathf.Rad2Deg;
            currentBrushRotation = Quaternion.Euler(0, angle, 0);
        }

        lastPosition = currentCenter;

        // Handle different paint modes
        switch (paintMode)
        {
            case PaintMode.Traditional:
                if (velocity.magnitude >= minSpeed)
                {
                    CheckTerrainContactSmooth();
                }
                break;

            case PaintMode.SplineBased:
                HandleSplineBasedPainting();
                break;

            case PaintMode.FieldPattern:
                HandleFieldPatternPainting();
                break;
        }

        // Handle spline recording
        HandleSplineRecording();

        UpdateUIText();

        // Input handling
        if (Input.GetKeyDown(KeyCode.R))
        {
            terrainData.SetAlphamaps(0, 0, originalAlphamaps);
        }

        if (Input.GetKeyDown(KeyCode.S))
        {
            ToggleSplineRecording();
        }

        if (Input.GetKeyDown(KeyCode.C))
        {
            ClearSpline();
        }
    }

    void CheckTerrainContactSmooth()
    {
        if (terrain == null) return;

        Vector3 objectCenter = GetObjectCenter();
        Vector3 rayStart = objectCenter + Vector3.up * 0.5f;

        if (Physics.Raycast(rayStart, Vector3.down, out RaycastHit hit, 2f))
        {
            if (hit.collider.GetComponent<TerrainCollider>())
            {
                Vector3 currentPaintPos = new Vector3(objectCenter.x + paintOffset.x, hit.point.y, objectCenter.z + paintOffset.z);
                float distance = Vector3.Distance(lastPaintPosition, currentPaintPos);
                float spacing = paintRadius * 0.5f;
                int steps = Mathf.CeilToInt(distance / spacing);

                for (int i = 0; i <= steps; i++)
                {
                    float t = (float)i / steps;
                    Vector3 interpolatedPos = Vector3.Lerp(lastPaintPosition, currentPaintPos, t);
                    if (IsTargetLayer(interpolatedPos))
                    {
                        PaintTextureAtWithIntensity(interpolatedPos, paintIntensity);
                    }
                }

                lastPaintPosition = currentPaintPos;
            }
        }
    }

    bool IsTargetLayer(Vector3 worldPos)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;
        int mapX = Mathf.RoundToInt((terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth);
        int mapZ = Mathf.RoundToInt((terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight);
        float[,,] alphamaps = terrainData.GetAlphamaps(mapX, mapZ, 1, 1);
        float targetWeight = alphamaps[0, 0, targetLayerIndex];
        return targetWeight >= 0.5f;
    }

    void PaintTextureAtWithIntensity(Vector3 worldPos, float intensity)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;
        float mapX = terrainPos.x / terrainData.size.x * terrainData.alphamapWidth;
        float mapZ = terrainPos.z / terrainData.size.z * terrainData.alphamapHeight;

        float brushSizeX = paintRadius / terrainData.size.x * terrainData.alphamapWidth;
        float brushSizeZ = paintRadius / terrainData.size.z * terrainData.alphamapHeight;
        float brushSize = Mathf.Max(brushSizeX, brushSizeZ);
        int halfBrush = Mathf.RoundToInt(brushSize * 0.5f);

        int centerX = Mathf.FloorToInt(mapX);
        int centerZ = Mathf.FloorToInt(mapZ);
        int startX = Mathf.Clamp(centerX - halfBrush, 0, terrainData.alphamapWidth - 1);
        int startZ = Mathf.Clamp(centerZ - halfBrush, 0, terrainData.alphamapHeight - 1);
        int endX = Mathf.Clamp(centerX + halfBrush, 0, terrainData.alphamapWidth - 1);
        int endZ = Mathf.Clamp(centerZ + halfBrush, 0, terrainData.alphamapHeight - 1);

        int actualWidth = endX - startX + 1;
        int actualHeight = endZ - startZ + 1;
        float[,,] alphaMaps = terrainData.GetAlphamaps(startX, startZ, actualWidth, actualHeight);

        for (int x = 0; x < actualWidth; x++)
        {
            for (int z = 0; z < actualHeight; z++)
            {
                float offsetX = x - (actualWidth * 0.5f);
                float offsetZ = z - (actualHeight * 0.5f);

                Vector3 rotated = currentBrushRotation * new Vector3(offsetX, 0, offsetZ);
                float centerDistX = rotated.x;
                float centerDistZ = rotated.z;
                float distance = Mathf.Sqrt(centerDistX * centerDistX + centerDistZ * centerDistZ);
                float maxRadius = Mathf.Min(actualWidth, actualHeight) * 0.5f;

                if (distance <= maxRadius)
                {
                    float falloff = Mathf.Clamp01((1f - (distance / maxRadius)) * intensity);

                    for (int i = 0; i < terrainData.alphamapLayers; i++)
                    {
                        if (i == applyLayerIndex)
                        {
                            alphaMaps[x, z, i] = Mathf.Lerp(alphaMaps[x, z, i], 1f, falloff);
                            if (alphaMaps[x, z, i] > 0.5f) paintedPixels++;
                        }
                        else
                        {
                            alphaMaps[x, z, i] = Mathf.Lerp(alphaMaps[x, z, i], 0f, falloff);
                        }
                    }
                }
            }
        }

        terrainData.SetAlphamaps(startX, startZ, alphaMaps);
        UpdateAreaProgress();
    }

    void InitializeAreaTracking()
    {
        switch (areaSize)
        {
            case AreaSize.Area_20x20: currentAreaSize = new Vector2(20, 20); break;
            case AreaSize.Area_50x50: currentAreaSize = new Vector2(50, 50); break;
            case AreaSize.Area_100x100: currentAreaSize = new Vector2(100, 100); break;
            case AreaSize.Custom: currentAreaSize = customAreaSize; break;
        }

        float pixelsPerMeterX = terrainData.alphamapWidth / terrainData.size.x;
        float pixelsPerMeterZ = terrainData.alphamapHeight / terrainData.size.z;
        totalAreaPixels = Mathf.RoundToInt(currentAreaSize.x * pixelsPerMeterX * currentAreaSize.y * pixelsPerMeterZ);

        paintedPixels = 0;
        currentCompletionPercentage = 0f;
        areaCompleted = false;

        if (percentageText != null) percentageText.text = "0.0%";
        if (progressSlider != null)
        {
            progressSlider.minValue = 0f;
            progressSlider.maxValue = targetCompletionPercentage;
            progressSlider.value = 0f;
        }

        if (completionPanel != null) completionPanel.SetActive(false);
    }

    void UpdateAreaProgress()
    {
        if (areaCompleted) return;

        if (totalAreaPixels > 0)
        {
            float completionRatio = (float)paintedPixels / totalAreaPixels;
            currentCompletionPercentage = completionRatio * targetCompletionPercentage;

            if (percentageText != null)
                percentageText.text = currentCompletionPercentage.ToString("F1") + "%";

            if (progressSlider != null)
                progressSlider.value = currentCompletionPercentage;

            if (currentCompletionPercentage >= targetCompletionPercentage)
            {
                areaCompleted = true;
                currentCompletionPercentage = targetCompletionPercentage;

                if (completionPanel != null) completionPanel.SetActive(true);
                Debug.Log("Area Completed! Percentage: " + currentCompletionPercentage + "%");
            }
        }
    }

    void UpdateUIText()
    {
        if (!areaCompleted && totalAreaPixels > 0)
        {
            float completionRatio = (float)paintedPixels / totalAreaPixels;
            float displayPercentage = completionRatio * targetCompletionPercentage;
            if (percentageText != null) percentageText.text = displayPercentage.ToString("F1") + "%";
            if (progressSlider != null) progressSlider.value = displayPercentage;
        }
    }

    [ContextMenu("Reset Area Progress")]
    public void ResetAreaProgress()
    {
        paintedPixels = 0;
        currentCompletionPercentage = 0f;
        areaCompleted = false;

        if (percentageText != null) percentageText.text = "0.0%";
        if (progressSlider != null) progressSlider.value = 0f;
        if (completionPanel != null) completionPanel.SetActive(false);

        Debug.Log("Area progress reset!");
    }

    // Spline-based painting methods
    void HandleSplineBasedPainting()
    {
        if (splinePath.Count < 2) return;

        Vector3 currentCenter = GetObjectCenter();

        if (velocity.magnitude >= minSpeed)
        {
            // Find closest point on spline
            Vector3 closestPoint = FindClosestPointOnSpline(currentCenter);
            float splineT = GetSplineParameter(closestPoint);

            // Paint along spline with variable width and intensity
            float currentWidth = Mathf.Lerp(paintRadius, splineWidth, splineIntensityCurve.Evaluate(splineT));
            float currentIntensity = paintIntensity * splineIntensityCurve.Evaluate(splineT);

            PaintAlongSpline(closestPoint, currentWidth, currentIntensity);
        }
    }

    void HandleFieldPatternPainting()
    {
        if (!useFieldPattern) return;

        Vector3 currentCenter = GetObjectCenter();

        if (velocity.magnitude >= minSpeed)
        {
            // Create field pattern painting
            Vector3 fieldDirection = velocity.normalized;
            Vector3 perpendicular = Vector3.Cross(fieldDirection, Vector3.up).normalized;

            // Paint multiple rows
            for (int i = -1; i <= 1; i++)
            {
                Vector3 rowOffset = perpendicular * (i * fieldRowSpacing);
                Vector3 paintPos = currentCenter + rowOffset;

                if (IsTargetLayer(paintPos))
                {
                    PaintTextureAtWithIntensity(paintPos, paintIntensity * 0.8f);
                }
            }
        }
    }

    void HandleSplineRecording()
    {
        if (!isRecordingSpline) return;

        Vector3 currentCenter = GetObjectCenter();

        if (Time.time - lastSplineRecordTime > splineResolution)
        {
            recordedPath.Add(currentCenter);
            lastSplineRecordTime = Time.time;

            // Auto-create spline points
            if (autoCreateSpline && recordedPath.Count > 1)
            {
                CreateSplineFromRecordedPath();
            }
        }
    }

    // Spline utility methods
    Vector3 FindClosestPointOnSpline(Vector3 position)
    {
        if (splinePath.Count < 2) return position;

        Vector3 closestPoint = splinePath[0];
        float closestDistance = Vector3.Distance(position, closestPoint);

        for (int i = 0; i < splinePath.Count - 1; i++)
        {
            Vector3 linePoint = GetClosestPointOnLine(splinePath[i], splinePath[i + 1], position);
            float distance = Vector3.Distance(position, linePoint);

            if (distance < closestDistance)
            {
                closestDistance = distance;
                closestPoint = linePoint;
            }
        }

        return closestPoint;
    }

    Vector3 GetClosestPointOnLine(Vector3 lineStart, Vector3 lineEnd, Vector3 point)
    {
        Vector3 lineDirection = lineEnd - lineStart;
        float lineLength = lineDirection.magnitude;
        lineDirection.Normalize();

        Vector3 pointDirection = point - lineStart;
        float projectedLength = Vector3.Dot(pointDirection, lineDirection);
        projectedLength = Mathf.Clamp(projectedLength, 0f, lineLength);

        return lineStart + lineDirection * projectedLength;
    }

    float GetSplineParameter(Vector3 point)
    {
        if (splinePath.Count < 2) return 0f;

        float totalLength = 0f;
        float currentLength = 0f;

        // Calculate total spline length
        for (int i = 0; i < splinePath.Count - 1; i++)
        {
            totalLength += Vector3.Distance(splinePath[i], splinePath[i + 1]);
        }

        // Find parameter based on closest point
        for (int i = 0; i < splinePath.Count - 1; i++)
        {
            Vector3 segmentStart = splinePath[i];
            Vector3 segmentEnd = splinePath[i + 1];
            Vector3 closestOnSegment = GetClosestPointOnLine(segmentStart, segmentEnd, point);

            if (Vector3.Distance(point, closestOnSegment) < 0.1f)
            {
                float segmentProgress = Vector3.Distance(segmentStart, closestOnSegment) / Vector3.Distance(segmentStart, segmentEnd);
                return (currentLength + segmentProgress * Vector3.Distance(segmentStart, segmentEnd)) / totalLength;
            }

            currentLength += Vector3.Distance(segmentStart, segmentEnd);
        }

        return 1f;
    }

    void PaintAlongSpline(Vector3 center, float width, float intensity)
    {
        // Paint with variable width along spline
        Vector3 direction = velocity.normalized;
        Vector3 perpendicular = Vector3.Cross(direction, Vector3.up).normalized;

        int samples = Mathf.CeilToInt(width / paintRadius);
        for (int i = -samples; i <= samples; i++)
        {
            float offset = (float)i / samples * width * 0.5f;
            Vector3 paintPos = center + perpendicular * offset;

            float falloff = 1f - Mathf.Abs((float)i / samples);
            float adjustedIntensity = intensity * falloff;

            if (IsTargetLayer(paintPos))
            {
                PaintTextureAtWithIntensity(paintPos, adjustedIntensity);
            }
        }
    }

    void CreateSplineFromRecordedPath()
    {
        if (recordedPath.Count < 3) return;

        splinePath.Clear();
        splinePoints.Clear();

        // Smooth the recorded path
        for (int i = 0; i < recordedPath.Count; i++)
        {
            Vector3 smoothedPoint = recordedPath[i];

            if (smoothSplinePainting && i > 0 && i < recordedPath.Count - 1)
            {
                Vector3 prev = recordedPath[i - 1];
                Vector3 next = recordedPath[i + 1];
                smoothedPoint = Vector3.Lerp(smoothedPoint, (prev + next) * 0.5f, splineSmoothing * 0.1f);
            }

            splinePath.Add(smoothedPoint);

            // Create spline points
            SplinePoint splinePoint = new SplinePoint(smoothedPoint);
            if (i > 0)
            {
                splinePoint.tangent = (smoothedPoint - splinePath[i - 1]).normalized;
            }
            splinePoints.Add(splinePoint);
        }
    }

    public void ToggleSplineRecording()
    {
        isRecordingSpline = !isRecordingSpline;

        if (isRecordingSpline)
        {
            recordedPath.Clear();
            splineStartPosition = GetObjectCenter();
            lastSplineRecordTime = Time.time;
            Debug.Log("Started spline recording");
        }
        else
        {
            if (recordedPath.Count > 1)
            {
                CreateSplineFromRecordedPath();
                Debug.Log($"Finished spline recording with {splinePath.Count} points");
            }
        }
    }

    public void ClearSpline()
    {
        splinePath.Clear();
        splinePoints.Clear();
        recordedPath.Clear();
        isRecordingSpline = false;
        Debug.Log("Spline cleared");
    }

    void OnDrawGizmos()
    {
        if (showDebugGizmos && Application.isPlaying)
        {
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(GetObjectCenter(), 0.5f);
            Vector3 paintPos = GetObjectCenter() + paintOffset;
            Gizmos.color = Color.red;
            if (useSquareBrush)
                Gizmos.DrawWireCube(paintPos, Vector3.one * paintRadius * 2f);
            else
                Gizmos.DrawWireSphere(paintPos, paintRadius);

            // Draw spline path
            if (splinePath.Count > 1)
            {
                Gizmos.color = Color.green;
                for (int i = 0; i < splinePath.Count - 1; i++)
                {
                    Gizmos.DrawLine(splinePath[i], splinePath[i + 1]);
                }
            }

            // Draw recorded path
            if (recordedPath.Count > 1)
            {
                Gizmos.color = Color.yellow;
                for (int i = 0; i < recordedPath.Count - 1; i++)
                {
                    Gizmos.DrawLine(recordedPath[i], recordedPath[i + 1]);
                }
            }
        }
    }
}
