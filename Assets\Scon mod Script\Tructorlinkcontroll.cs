using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class Tructorlinkcontroll : MonoBehaviour
{
    public Button Uplink;
    public Button Downlink;
    public GameObject tractorlink;
    public  ParticleSystem[] particle;
    public float rotationSpeed = 30f;
    public float maxRotationLimit = 12f;
    public float minRotationLimit = -25f;
    private bool isUpPressed = false;
    private bool isDownPressed = false;
    private RCC_CarControllerV3 carController;

    void Start()
    {
        // Add button press/release events
        AddButtonEvents(Uplink, () => isUpPressed = true, () => isUpPressed = false);
        AddButtonEvents(Downlink, () => isDownPressed = true, () => isDownPressed = false);

        // Find the RCC car controller
        carController = FindObjectOfType<RCC_CarControllerV3>();
    }

    void Update()
    {
        if (tractorlink != null)
        {
            Vector3 currentRotation = tractorlink.transform.localEulerAngles;
            float currentX = currentRotation.x > 180 ? currentRotation.x - 360 : currentRotation.x;

            if (isUpPressed && currentX < maxRotationLimit)
            {
                currentX += rotationSpeed * Time.deltaTime;
                currentX = Mathf.Clamp(currentX, minRotationLimit, maxRotationLimit);
                tractorlink.transform.localEulerAngles = new Vector3(currentX, currentRotation.y, currentRotation.z);
            }
            else if (isDownPressed && currentX > minRotationLimit)
            {
                currentX -= rotationSpeed * Time.deltaTime;
                currentX = Mathf.Clamp(currentX, minRotationLimit, maxRotationLimit);
                tractorlink.transform.localEulerAngles = new Vector3(currentX, currentRotation.y, currentRotation.z);
            }

            // Control particle systems based on rotation and speed
            if (particle != null && particle.Length > 0)
            {
                // Check if speed is greater than 5 and rotation is within limits
                bool speedCondition = carController != null && carController.speed > 5f;
                bool rotationCondition = currentX >= -25f && currentX <= -8f;

                if (speedCondition && rotationCondition)
                {
                    // Play all particle systems in the array
                    foreach (ParticleSystem ps in particle)
                    {
                        if (ps != null && !ps.isPlaying)
                            ps.Play();
                    }
                }
                else
                {
                    // Stop all particle systems in the array
                    foreach (ParticleSystem ps in particle)
                    {
                        if (ps != null && ps.isPlaying)
                            ps.Stop();
                    }
                }
            }
        }
    }

    void AddButtonEvents(Button button, System.Action onPress, System.Action onRelease)
    {
        EventTrigger trigger = button.gameObject.GetComponent<EventTrigger>();
        if (trigger == null) trigger = button.gameObject.AddComponent<EventTrigger>();

        EventTrigger.Entry pressEntry = new EventTrigger.Entry();
        pressEntry.eventID = EventTriggerType.PointerDown;
        pressEntry.callback.AddListener((data) => onPress());
        trigger.triggers.Add(pressEntry);

        EventTrigger.Entry releaseEntry = new EventTrigger.Entry();
        releaseEntry.eventID = EventTriggerType.PointerUp;
        releaseEntry.callback.AddListener((data) => onRelease());
        trigger.triggers.Add(releaseEntry);
    }
}
