using UnityEngine;
using UnityEngine.UI;

public enum AreaSize
{
    Area_20x20,
    Area_50x50,
    Area_100x100,
    Custom
}

public class TerrainPainter : MonoBehaviour
{
    public float paintRadius = 2f;
    public int targetLayerIndex = 0;
    public int applyLayerIndex = 1;
    public float minSpeed = 0.1f;
    public float maxSpeed = 10f;
    public float paintIntensity = 1f;
    public bool useSquareBrush = false;
    public Vector3 paintOffset = Vector3.zero;
    public bool showDebugGizmos = false;
    public float paintFrequency = 0.1f;

    [Header("Area Completion Settings")]
    public AreaSize areaSize = AreaSize.Area_50x50;
    public Vector2 customAreaSize = new Vector2(50, 50);
    public GameObject completionPanel;
    public Text percentageText;
    public Slider progressSlider;
    public float targetCompletionPercentage = 100f;

    [Header("Area Progress")]
    public float currentCompletionPercentage = 0f;
    public bool areaCompleted = false;

    private Terrain terrain;
    private TerrainData terrainData;
    private float[,,] originalAlphamaps;
    private Vector3 lastPosition;
    private Vector3 velocity;
    private Renderer objectRenderer;
    private Vector3 lastPaintPosition;
    private Quaternion currentBrushRotation = Quaternion.identity;

    private Vector2 currentAreaSize;
    private int totalAreaPixels;
    private int paintedPixels;

    void Start()
    {
        terrain = Terrain.activeTerrain;
        objectRenderer = GetComponent<Renderer>();

        if (terrain != null)
        {
            terrainData = terrain.terrainData;
            originalAlphamaps = terrainData.GetAlphamaps(0, 0, terrainData.alphamapWidth, terrainData.alphamapHeight);
        }

        lastPosition = GetObjectCenter();
        lastPaintPosition = lastPosition + paintOffset;
        InitializeAreaTracking();
    }

    Vector3 GetObjectCenter()
    {
        return objectRenderer != null ? objectRenderer.bounds.center : transform.position;
    }

    void Update()
    {
        Vector3 currentCenter = GetObjectCenter();
        velocity = (currentCenter - lastPosition) / Time.deltaTime;

        if (velocity.sqrMagnitude > 0.001f)
        {
            float angle = Mathf.Atan2(velocity.x, velocity.z) * Mathf.Rad2Deg;
            currentBrushRotation = Quaternion.Euler(0, angle, 0);
        }

        lastPosition = currentCenter;

        if (velocity.magnitude >= minSpeed)
        {
            CheckTerrainContactSmooth();
        }

        UpdateUIText();

        if (Input.GetKeyDown(KeyCode.R))
        {
            terrainData.SetAlphamaps(0, 0, originalAlphamaps);
        }
    }

    void CheckTerrainContactSmooth()
    {
        if (terrain == null) return;

        Vector3 objectCenter = GetObjectCenter();
        Vector3 rayStart = objectCenter + Vector3.up * 0.5f;

        if (Physics.Raycast(rayStart, Vector3.down, out RaycastHit hit, 2f))
        {
            if (hit.collider.GetComponent<TerrainCollider>())
            {
                Vector3 currentPaintPos = new Vector3(objectCenter.x + paintOffset.x, hit.point.y, objectCenter.z + paintOffset.z);
                float distance = Vector3.Distance(lastPaintPosition, currentPaintPos);
                float spacing = paintRadius * 0.5f;
                int steps = Mathf.CeilToInt(distance / spacing);

                for (int i = 0; i <= steps; i++)
                {
                    float t = (float)i / steps;
                    Vector3 interpolatedPos = Vector3.Lerp(lastPaintPosition, currentPaintPos, t);
                    if (IsTargetLayer(interpolatedPos))
                    {
                        PaintTextureAtWithIntensity(interpolatedPos, paintIntensity);
                    }
                }

                lastPaintPosition = currentPaintPos;
            }
        }
    }

    bool IsTargetLayer(Vector3 worldPos)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;
        int mapX = Mathf.RoundToInt((terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth);
        int mapZ = Mathf.RoundToInt((terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight);
        float[,,] alphamaps = terrainData.GetAlphamaps(mapX, mapZ, 1, 1);
        float targetWeight = alphamaps[0, 0, targetLayerIndex];
        return targetWeight >= 0.5f;
    }

    void PaintTextureAtWithIntensity(Vector3 worldPos, float intensity)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;
        float mapX = terrainPos.x / terrainData.size.x * terrainData.alphamapWidth;
        float mapZ = terrainPos.z / terrainData.size.z * terrainData.alphamapHeight;

        float brushSizeX = paintRadius / terrainData.size.x * terrainData.alphamapWidth;
        float brushSizeZ = paintRadius / terrainData.size.z * terrainData.alphamapHeight;
        float brushSize = Mathf.Max(brushSizeX, brushSizeZ);
        int halfBrush = Mathf.RoundToInt(brushSize * 0.5f);

        int centerX = Mathf.FloorToInt(mapX);
        int centerZ = Mathf.FloorToInt(mapZ);
        int startX = Mathf.Clamp(centerX - halfBrush, 0, terrainData.alphamapWidth - 1);
        int startZ = Mathf.Clamp(centerZ - halfBrush, 0, terrainData.alphamapHeight - 1);
        int endX = Mathf.Clamp(centerX + halfBrush, 0, terrainData.alphamapWidth - 1);
        int endZ = Mathf.Clamp(centerZ + halfBrush, 0, terrainData.alphamapHeight - 1);

        int actualWidth = endX - startX + 1;
        int actualHeight = endZ - startZ + 1;
        float[,,] alphaMaps = terrainData.GetAlphamaps(startX, startZ, actualWidth, actualHeight);

        for (int x = 0; x < actualWidth; x++)
        {
            for (int z = 0; z < actualHeight; z++)
            {
                float offsetX = x - (actualWidth * 0.5f);
                float offsetZ = z - (actualHeight * 0.5f);

                Vector3 rotated = currentBrushRotation * new Vector3(offsetX, 0, offsetZ);
                float centerDistX = rotated.x;
                float centerDistZ = rotated.z;
                float distance = Mathf.Sqrt(centerDistX * centerDistX + centerDistZ * centerDistZ);
                float maxRadius = Mathf.Min(actualWidth, actualHeight) * 0.5f;

                if (distance <= maxRadius)
                {
                    float falloff = Mathf.Clamp01((1f - (distance / maxRadius)) * intensity);

                    for (int i = 0; i < terrainData.alphamapLayers; i++)
                    {
                        if (i == applyLayerIndex)
                        {
                            alphaMaps[x, z, i] = Mathf.Lerp(alphaMaps[x, z, i], 1f, falloff);
                            if (alphaMaps[x, z, i] > 0.5f) paintedPixels++;
                        }
                        else
                        {
                            alphaMaps[x, z, i] = Mathf.Lerp(alphaMaps[x, z, i], 0f, falloff);
                        }
                    }
                }
            }
        }

        terrainData.SetAlphamaps(startX, startZ, alphaMaps);
        UpdateAreaProgress();
    }

    void InitializeAreaTracking()
    {
        switch (areaSize)
        {
            case AreaSize.Area_20x20: currentAreaSize = new Vector2(20, 20); break;
            case AreaSize.Area_50x50: currentAreaSize = new Vector2(50, 50); break;
            case AreaSize.Area_100x100: currentAreaSize = new Vector2(100, 100); break;
            case AreaSize.Custom: currentAreaSize = customAreaSize; break;
        }

        float pixelsPerMeterX = terrainData.alphamapWidth / terrainData.size.x;
        float pixelsPerMeterZ = terrainData.alphamapHeight / terrainData.size.z;
        totalAreaPixels = Mathf.RoundToInt(currentAreaSize.x * pixelsPerMeterX * currentAreaSize.y * pixelsPerMeterZ);

        paintedPixels = 0;
        currentCompletionPercentage = 0f;
        areaCompleted = false;

        if (percentageText != null) percentageText.text = "0.0%";
        if (progressSlider != null)
        {
            progressSlider.minValue = 0f;
            progressSlider.maxValue = targetCompletionPercentage;
            progressSlider.value = 0f;
        }

        if (completionPanel != null) completionPanel.SetActive(false);
    }

    void UpdateAreaProgress()
    {
        if (areaCompleted) return;

        if (totalAreaPixels > 0)
        {
            float completionRatio = (float)paintedPixels / totalAreaPixels;
            currentCompletionPercentage = completionRatio * targetCompletionPercentage;

            if (percentageText != null)
                percentageText.text = currentCompletionPercentage.ToString("F1") + "%";

            if (progressSlider != null)
                progressSlider.value = currentCompletionPercentage;

            if (currentCompletionPercentage >= targetCompletionPercentage)
            {
                areaCompleted = true;
                currentCompletionPercentage = targetCompletionPercentage;

                if (completionPanel != null) completionPanel.SetActive(true);
                Debug.Log("Area Completed! Percentage: " + currentCompletionPercentage + "%");
            }
        }
    }

    void UpdateUIText()
    {
        if (!areaCompleted && totalAreaPixels > 0)
        {
            float completionRatio = (float)paintedPixels / totalAreaPixels;
            float displayPercentage = completionRatio * targetCompletionPercentage;
            if (percentageText != null) percentageText.text = displayPercentage.ToString("F1") + "%";
            if (progressSlider != null) progressSlider.value = displayPercentage;
        }
    }

    [ContextMenu("Reset Area Progress")]
    public void ResetAreaProgress()
    {
        paintedPixels = 0;
        currentCompletionPercentage = 0f;
        areaCompleted = false;

        if (percentageText != null) percentageText.text = "0.0%";
        if (progressSlider != null) progressSlider.value = 0f;
        if (completionPanel != null) completionPanel.SetActive(false);

        Debug.Log("Area progress reset!");
    }

    void OnDrawGizmos()
    {
        if (showDebugGizmos && Application.isPlaying)
        {
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(GetObjectCenter(), 0.5f);
            Vector3 paintPos = GetObjectCenter() + paintOffset;
            Gizmos.color = Color.red;
            if (useSquareBrush)
                Gizmos.DrawWireCube(paintPos, Vector3.one * paintRadius * 2f);
            else
                Gizmos.DrawWireSphere(paintPos, paintRadius);
        }
    }
}
